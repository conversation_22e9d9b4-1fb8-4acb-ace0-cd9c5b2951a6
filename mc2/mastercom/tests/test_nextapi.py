# base test class
from django.test import TestCase
from django.contrib.auth import get_user_model
from unittest.mock import patch, Mock

from mc2.mastercom.next_api import NextApi


class NextApiTest(TestCase):

    def test_set_token(self):
        next_api = NextApi()
        next_api.set_token('test')
        self.assertIsNotNone(next_api.token)

    @patch('requests.get', return_value="{'type': {'mc2': 4}, 'usr': 'test', 'couch_id': 'test'}")
    def test_verify_ok(self, mock):
        next_api = NextApi()
        user, token = next_api.verify('ok_tk')

        User = get_user_model()
        db_user = User.objects.get(username='test')
        self.assertEqual(user, db_user)
        self.assertEqual(token, 'ok_tk')

    @patch('requests.get', return_value=Mock(status_code=401))
    def test_verify_bad_token(self, mock):
        next_api = NextApi()
        self.assertFalse(next_api.verify('bad_tk'))
