from unittest.mock import patch
from django.urls import reverse
from django.contrib.auth import get_user_model

from rest_framework.test import APITestCase
from rest_framework import status

from mc2.institute.tests.factories import InstituteFactory


class InstituteListViewTest(APITestCase):
    def setUp(self):
        User = get_user_model()
        self.url = reverse('institute-list')
        self.institute = InstituteFactory()
        self.user = User.objects.create_user(username='test', password='test')

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value={'type': {'mc2': 4}, 'usr': 'test', 'couch_id': 'test'})
    def test_get_institute_list_with_token_authentication(self, mock_verify):
        response = self.client.get(self.url, HTTP_AUTHORIZATION='valid_token')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], self.institute.name)

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value=False)
    def test_get_institute_list_with_invalid_token(self, mock_verify):
        response = self.client.get(self.url, HTTP_AUTHORIZATION='invalid_token')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
