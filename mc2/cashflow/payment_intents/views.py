from rest_framework import viewsets
from rest_framework import permissions
from rest_framework import status
from rest_framework.response import Response
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>

from django.utils.translation import gettext as _
from django.core.exceptions import ObjectDoesNotExist
from django.http import HttpResponse
from ..models import CcpPaymentIntents as PaymentIntent, CcpMovement, CcpType
from mc2.common.models import Parameter

from mc2.authentication.base import IsLocalhost
from .serializers import PaymentIntentSerializer

from .utils import crea_payment_intents, get_totale_intents, init_vendor, init_bank

from django.core.exceptions import ValidationError
from django.http import Http404
from django.shortcuts import get_object_or_404 as _get_object_or_404

from django.db.models import Sum
import logging
import json

class PaymentIntentViewSet(viewsets.ModelViewSet):
    queryset = PaymentIntent.objects.all()
    serializer_class = PaymentIntentSerializer
    permission_classes = [IsLocalhost]
    vendor_class = None
    lookup_field = 'token'
    def dispatch(self, request, *args, **kwargs):
        headers = request.headers

        client_name = headers.get('X-Client-Name', None)
        client_version = headers.get('X-Client-Version', None)
        client_platform = headers.get('X-Client-Platform', None)

        if not client_name or not client_version or not client_platform:
            data = {'result': 'KO', 'messaggio': _('client info missing in header')}
            response = Response(data)
            response.accepted_renderer = JSONRenderer()
            response.accepted_media_type = "application/json"
            response.renderer_context = {
                "request": request,
                "view": None,  # Or the view instance if applicable
            }
            response.status_code = status.HTTP_400_BAD_REQUEST

            return response



        return super().dispatch(request, *args, **kwargs)



    def create(self, request):

        # logger = logging.getLogger(__name__)
        # logger.info('Creating payment intent')


        json_data = json.loads(request.body)

        # try:
        #     logger.info(f"Request method: {request.method}")
        #     logger.info(f"Request size: {request.META.get('CONTENT_LENGTH')}")
        #     logger.info(f"Request headers: {request.headers}")
        #     logger.info(f"Request raw body: {request.body}")
        #     logger.info(request.data)
        # except Exception as e:
        #     logger.error(f"Error processing request: {e}")

        anno_scolastico = request.data.get('anno_scolastico', None)
        utente = request.data.get('utente', None)
        tipo_utente = request.data.get('tipo_utente', None)
        importo = request.data.get('importo', None)
        articoli = request.data.get('articoli', None)
        id_studente = request.data.get('id_studente', None)
        banca = request.data.get('banca', None)
        canale = request.data.get('canale', None)


        self.vendor_class = init_vendor(canale)

        if not self.vendor_class:
            return Response({'result': 'KO', 'messaggio': _('channel not valid')}, status=status.HTTP_400_BAD_REQUEST)

        banca = init_bank(request)
        if not banca:
            return Response({'result': 'KO', 'messaggio': _('bank not valid')}, status=status.HTTP_400_BAD_REQUEST)

        if not anno_scolastico:
            return Response({'result': 'KO', 'messaggio': _('%(variable)s is required') % {'variable': 'anno scolastico'} }, status=status.HTTP_400_BAD_REQUEST)

        if not utente or not tipo_utente:
            return Response({'result': 'KO', 'messaggio': _('%(variable)s is required') % {'variable': 'utente'}}, status=status.HTTP_400_BAD_REQUEST)

        if not importo:
            return Response({'result': 'KO', 'messaggio': _('%(variable)s is required') % {'variable': 'importo'}}, status=status.HTTP_400_BAD_REQUEST)

        if not articoli or not len(articoli):
            return Response({'result': 'KO', 'messaggio': _('%(variable)s is required') % {'variable': 'articoli'}}, status=status.HTTP_400_BAD_REQUEST)

        try:
            channel_intents_data = self.vendor_class.get_channel_intents_data()
            intents = crea_payment_intents(articoli, utente, id_studente, anno_scolastico, banca=banca, request=request.data, channel_intents_data=channel_intents_data)
            totale = get_totale_intents(intents)

        except ObjectDoesNotExist as e:
           return Response({'result': 'KO', 'messaggio': _('one or more items in cart do not exist')}, status=status.HTTP_400_BAD_REQUEST)

        if totale <= 0:
            return Response({'result': 'KO', 'messaggio': _('cart amount not valid')}, status=status.HTTP_400_BAD_REQUEST)

        payment_intent = self.vendor_class.get_token(totale, intents)

        for intent in intents:
            intent.token = payment_intent['id']
            intent.save()

        try:
            stripe_client_secret = payment_intent['client_secret']
        except Exception as e:
            stripe_client_secret = None

        result = {
            'transaction_id': payment_intent['id'],
            'stripe_client_secret': stripe_client_secret,
            'stripe_publishable_key': self.vendor_class.get_publishable_key()
        }

        return Response(result, status=status.HTTP_201_CREATED)

    # fornisce i dettagli della transazione e il riepilogo degli articoli acquistati
    def retrieve(self, request, token, *args, **kwargs):

        payment_intents = PaymentIntent.objects.filter(token=token)
        if not payment_intents:
            return Response({'result': 'KO', 'messaggio': _('payment intent not found')}, status=status.HTTP_404_NOT_FOUND)

        payment_intents = list(payment_intents.all())

        msg_ok = _('Transaction successful')
        msg_ko = _('Transaction failed')

        result = {
            'data_creazione': payment_intents[0].date_created,
            'data_conferma': payment_intents[0].date_succeeded,
            'messaggio_esito': f"{msg_ok if payment_intents[0].succeeded() else msg_ko}",
            'confermato': payment_intents[0].succeeded(),
            'totale': payment_intents[0].transaction_total(),
            'canale_pagamento': payment_intents[0].payment_channel,
            'token': payment_intents[0].token,
            'articoli_acquistati': payment_intents[0].items(),
            'numero_articoli': len(payment_intents[0].items()),
            'movimenti_generati': payment_intents[0].sito_app_movements(),
        }

        return Response(result, status=status.HTTP_200_OK)


    # restituisce l'elenco delle transazioni online effettuate per il singolo studente
    def list(self, request):
        id_studente = request.data.get('id_studente', None)
        id_studente = request.query_params.get('id_studente', None)
        try:
            id_studente = int(id_studente)
        except:
            pass

        if not id_studente:
            return Response({'result': 'KO', 'messaggio': _('id_studente is required')}, status=status.HTTP_400_BAD_REQUEST)

        payment_intents = PaymentIntent.objects.filter(subject_id=id_studente).order_by('-date_created')

        apply_filter = True
        # Se il parametro all è presente, non applico il filtro e mostro tutte i tentativi di transazione
        if request.query_params.get('all', None):
            apply_filter = False

        if apply_filter:
            payment_intents = payment_intents.filter(date_succeeded__isnull=False)

        payment_intents_list = list(payment_intents.all())
        result = []
        for intent in payment_intents_list:
            items = intent.items()
            result.append({
                'data_creazione': intent.date_created,
                'data_conferma': intent.date_succeeded,
                'messaggio_esito': '',
                'confermato': intent.succeeded(),
                'totale': intent.transaction_total(),
                'canale_pagamento': intent.payment_channel,
                'token': intent.token,
                'articoli_acquistati': items,
                'numero_articoli': len(items),
            })





        return Response(result, status=status.HTTP_200_OK)




    def update(self, request, pk=None):
        pass




