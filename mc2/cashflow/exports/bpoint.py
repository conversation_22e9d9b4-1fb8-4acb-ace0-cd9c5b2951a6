import re
import zipfile
import io
from datetime import datetime, timedelta

from django import forms
from django.http import FileResponse

from mc2.institute.utils import get_institute
from mc2.common.exporter.base import Serializer
from mc2.common.exporter.views import ExportAPIView
from mc2.cashflow.models import CcpPayment as Payment
from mc2.common.exporter.columns import TextColumn, NumericColumn, DateColumn
from mc2.authentication.base import IsLocalhost
from mc2.cashflow.movements.utils import get_list as get_movements_list
from mc2.cashflow.payments.utils import get_list as get_payments_list
from mc2.mastercom.next_api import NextApi




"""
https://docs.google.com/spreadsheets/d/1zShyUYclFmamHY9GKVFY6lySDIRtExnQpu_Z9baMtkI/edit?gid=1434355247#gid=1434355247
"""
class Movim(Serializer):
    fiscal_code = TextColumn(range=[1, 16], required=True) # codice fiscale
    vat_number = TextColumn(range=[17, 27], required=True) # partita iva
    company_name = TextColumn(range=[28, 77], required=True) # ragione sociale
    vat_year = NumericColumn(align='right', fill_char='0', range=[78, 79], required=True) # esercizio iva
    school_year = NumericColumn(align='right', fill_char='0', range=[80, 83], required=True) # esercizio CO.GE
    topic = TextColumn(range=[84, 84], required=True, default='P') #  Argomento -> sempre 'P'
    responsability = TextColumn(range=[85, 85], required=True, default='N') # Per la prima nota sempre N
    partita = NumericColumn(align='right', fill_char='0', range=[86, 92], required=True) # Numero partita
    internal_counter = NumericColumn(align='right', fill_char='0', range=[93, 95], required=True) # Progressivo dell'operazione
    operation_date = DateColumn(align='right', range=[96, 101], required=True, format="%y%m%d") # Data operazione formato AAMMGG
    code = TextColumn(align='left', fill_char=' ', range=[102, 113], required=True) # Codice conto -> codice conto tipo movimento da 6 caratteri + 6 spazi
    register_type = NumericColumn(align='right', fill_char='0', range=[114, 115], required=True, default=1) # Tipo registro -> Probabilmente per noi è sempre 01. 04 per i pagamenti?
    register_code = NumericColumn(align='right', fill_char='0', range=[116, 117], required=True, default=1) # Codice registro
    protocol_number = NumericColumn(align='right', fill_char='0', range=[118, 124], required=True, default=0) # Numero protocollo -> 7 zeri
    article_type = NumericColumn(align='right', fill_char='0', range=[125, 125], required=True, default=0) # Tipo articolo -> PER I MOVIMENTI DELLA SCUOLA DOVREBBE ESSERE SEMPRE 0
    movement_type = TextColumn(range=[126, 126], required=True, default='R') # Tipo movimento
    cost_code = NumericColumn(align='right', fill_char='0', range=[127, 129],  default=0) # Codice c/costo
    reason_code = NumericColumn(align='right', fill_char='0', range=[130, 134], required=True) # Codice causale -> I codici causali attualmente implementati sono sempre 00150 per i movimenti in DARE e 00160 per quelli in AVERE
    reason_number = NumericColumn(align='right', fill_char='0', range=[135, 135], required=True, default=0) # Numero causale -> 0
    amount_sign = TextColumn(range=[136, 136], required=True) # Numero causale -> P positivo, N negativo
    amount = NumericColumn(align='right', fill_char='0', range=[137, 149], required=True) # Importo
    amount_accounting_sign = TextColumn(range=[150, 150], required=True) # Segno contabile importo -> D Dare, A Avere
    annotations = TextColumn(range=[151, 180]) # Note -> forzare a spazi
    document_date = DateColumn(align='right', range=[181, 186], required=True, format="%y%m%d") # Data documento formato AAMMGG
    document_number = NumericColumn(align='right', fill_char=' ', range=[187, 193]) # Numero documento -> Per i movimento contabili e corrispettivi si può forzare "spaces"
    anagraphic_type = TextColumn(range=[194, 194]) # Tipo anagrafica -> sempre spazi per persona giuridica
    history_flag = TextColumn(range=[195, 195]) # Flag storico -> sempre spazi
    free = TextColumn(range=[196, 215]) # Spazi


    separator = ';'
    header = True
    lineterminator = '\r\n'


"""
https://docs.google.com/spreadsheets/d/1zShyUYclFmamHY9GKVFY6lySDIRtExnQpu_Z9baMtkI/edit?gid=**********#gid=**********
"""
class Clisisp(Serializer):
    """
        Codice anagrafico cliente e deve corrispondere ai secondi 6 caratteri del codice conto di MOVIM.
        Si  ricorda  che  il  codice  Cli/For  su MOVIM   deve   essere   registrato   sul primo  record  della  partita  se  fattura
        acquisto o vendita
    """
    code = TextColumn(align='left', fill_char=' ', range=[1, 6], required=True) #  codice_conto_bpoint nel campo personalizzato

    fiscal_code = TextColumn(range=[7, 22], required=True) # codice fiscale studente
    vat_number = TextColumn(range=[23, 33], fill_char=' ') # partita iva
    type = TextColumn(range=[34, 34], required=True, default='F') # tipo anagrafica => 'F' = Persona fisica
    full_name = TextColumn(range=[35, 84], required=True, fill_char=' ') # cognome e nome studente
    address = TextColumn(range=[85, 112], required=True) # via
    address_number = TextColumn(range=[113, 119], required=False, fill_char=' ') # civico
    city = TextColumn(range=[120, 154], required=True) # comune
    zip_code = TextColumn(fill_char='0', range=[155, 159], required=True) # cap

    empty = TextColumn(range=[160, 304]) # spazi

    separator = ';'
    header = True
    lineterminator = '\r\n'


class BPointFilterForm(forms.Form):
    movement_ids = forms.CharField(required=False)
    payment_ids = forms.CharField(required=False)

    def __init__(self, data=None, *args, **kwargs):
        super().__init__(data or dict(), *args, **kwargs)

    def clean_movement_ids(self):
        movement_ids = self.cleaned_data.get('movement_ids')
        try:
            return [int(m_id) for m_id in movement_ids.split(',')] if movement_ids else []
        except ValueError:
            self.add_error('movement_ids', 'Enter a valid list of integers.')

    def clean_payment_ids(self):
        payment_ids = self.cleaned_data.get('payment_ids')
        try:
            return [int(p_id) for p_id in payment_ids.split(',')] if payment_ids else []
        except ValueError:
            self.add_error('payment_ids', 'Enter a valid list of integers.')


    def is_valid(self):
        if not super().is_valid():
            return False

        # One and only one field must be populated
        if not self.cleaned_data.get('movement_ids') and not self.cleaned_data.get('payment_ids'):
            self.add_error(None, 'At least one of movement_ids or payment_ids must be provided.')
            return False
        if self.cleaned_data.get('movement_ids') and self.cleaned_data.get('payment_ids'):
            self.add_error(None, 'Only one of movement_ids or payment_ids must be provided.')
            return False
        return True

class BPointExportAPIView(ExportAPIView):
    permission_classes = [IsLocalhost]
    serializer_cls = Movim
    filter_form_cls = BPointFilterForm
    errors = []
    clisisp_data = []
    last_movement_id = None

    def get_students_personal_fields(self):
        next_api = NextApi()
        next_api.set_token(self.request.headers.get('Authorization'))
        response = next_api.get('/next-api/v1/students/dati_personalizzati')

        results = []
        for student in response:
            if 'codice_conto_bpoint' in student['dati_personalizzati']:
                results.append({
                    'code': student['dati_personalizzati']['codice_conto_bpoint']['valore'],
                    'fiscal_code': student['codice_fiscale'],
                    'full_name': student['cognome'] + ' ' + student['nome'],
                    'address': student['indirizzo'][:28],
                    'city': student['comune_residenza'],
                    'zip_code': student['cap_residenza'],
                    'id': student['id_studente']
                })
        return results

    def get_data(self, filter):
        results = None
        self.clisisp_data = self.get_students_personal_fields()
        if filter.get('movement_ids'):
            results = get_movements_list({'ids': filter['movement_ids']}).order_by('id')
        elif filter.get('payment_ids'):
            results = get_payments_list({'ids': filter['payment_ids']}).order_by('movement__id')

        return results

    def movement_changed(self, movement_id):
        if self.last_movement_id != movement_id:
            self.last_movement_id = movement_id
            return True
        return False

    def get_sign(self, incoming: bool, amount: float) -> str:
        if amount < 0:
            return 'N'
        return 'P' if incoming else 'N'

    def build_movements(self, items):
        partita = 0
        res = []
        internal_counter = 1
        for item in items:
            # find in students_data the element has student_id = item.subject_id
            student_data = next((x for x in self.clisisp_data if x['id'] == item.subject_id), None)
            student_data_code = student_data['code'] if student_data else ''

            partita += 1
            creation_date = datetime.fromtimestamp(item.creation_date)
            school_year = int(datetime.now().strftime('%y')*2)
            """
                In attesa di capire cosa ci va visto che da documentazione è un campo numerico e loro lo hanno popolato
                con del testo tipo 'ISCRIZIONI, RETTE' per ora lo teniamo a None => 00
                cost_code = int(item.type.centro_costo_ricavo) if item.type.centro_costo_ricavo else None
            """
            cost_code = None
            amount = int(round(abs(item.amount)*100,0))
            res += [{   # DARE -> MOVIMENTO
                'fiscal_code': self.institute.school_fiscal_code,
                'vat_number': self.institute.fiscal_code,
                'company_name': self.institute.name,
                'vat_year': int(creation_date.strftime('%y')),
                'school_year': school_year,
                'partita': partita,
                'internal_counter': internal_counter,
                'operation_date': creation_date,
                'code': item.type.easy_code + student_data_code,
                'cost_code': cost_code,
                'reason_code': 150,
                'amount': amount,
                'amount_sign': self.get_sign(item.type.incoming, item.amount),
                'amount_accounting_sign': 'D',
                'document_date': creation_date,
                'annotations': re.sub(r'[^a-zA-Z0-9\s]', '', item.type.short_desc[:30] if item.type.short_desc else item.type.name[:30])
            }]
        return res

    def build_payments(self, items):
        res = []
        partita = 0
        internal_counter = 0
        for item in items:
            if self.movement_changed(item.movement.id):
                partita += 1
                internal_counter = 0
            internal_counter += 1

            # find in students_data the element has student_id = item.subject_id
            student_data = next((x for x in self.clisisp_data if x['id'] == item.movement.subject_id), None)
            student_data_code = student_data['code'] if student_data else ''


            accountable_date = datetime.fromtimestamp(item.accountable_date)
            school_year = int(datetime.now().strftime('%y')*2)
            amount = int(round(abs(item.amount)*100,0))
            res += [{ # AVERE -> PAGAMENTO
                'fiscal_code': self.institute.school_fiscal_code,
                'vat_number': self.institute.fiscal_code,
                'company_name': self.institute.name,
                'vat_year': int(accountable_date.strftime('%y')),
                'school_year': school_year,
                'partita': partita,
                'internal_counter': internal_counter,
                'operation_date': accountable_date,
                'code': item.get_piano_conti() + student_data_code,
                'reason_code': 160,
                'amount': amount,
                'amount_sign': self.get_sign(item.movement.type.incoming, item.amount),
                'amount_accounting_sign': 'A',
                'document_date': accountable_date,
                'annotations': re.sub(r'[^a-zA-Z0-9\s]', '', item.movement.type.short_desc[:30] if item.movement.type.short_desc else item.movement.type.name[:30])
            }]
        return res

    def build(self, params={}) -> list|bool:
        self.institute = get_institute()
        items = self.get_data(params)

        res = []
        if params.get('movement_ids'):
            res = self.build_movements(items)
        elif params.get('payment_ids'):
            res = self.build_payments(items)

        return res

    def export(self, data, *args, **kwargs):
        export_movin = self.RESPONSES_MAP.get('txt')(data)
        content_movin = export_movin.raw_content

        self.serializer_cls = Clisisp
        export_clisisp = self.RESPONSES_MAP.get('txt')(self.serialize(self.clisisp_data))
        content_clisisp = export_clisisp.raw_content

        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.writestr('movim', content_movin)
            zipf.writestr('ivamov', '')
            zipf.writestr('clisisp', content_clisisp)
        zip_buffer.seek(0)
        zip_filename = f"export_gest_{datetime.now().strftime('%Y%m%d%H%M')}.zip"

        return FileResponse(zip_buffer, as_attachment=True, filename=zip_filename, content_type='application/zip')
