
from django.test import TestCase
from rest_framework.test import APIRequestFactory
from unittest.mock import patch
from mc2.authentication.base import NextApiAuthentication


class NextApiAuthenticationTest(TestCase):
    def setUp(self):
        self.factory = APIRequestFactory()
        self.authentication = NextApiAuthentication()

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value={'type': {'mc2': 4}, 'usr': 'test', 'couch_id': 'test'})
    def test_authenticate_with_valid_token(self, mock_verify):
        request = self.factory.get('/institute/', HTTP_AUTHORIZATION='valid_token')
        user, token = self.authentication.authenticate(request)
        self.assertIsNotNone(user)
        self.assertEqual(token, 'valid_token')

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value=False)
    def test_authenticate_with_invalid_token(self, mock_verify):
        request = self.factory.get('/institute/', HTTP_AUTHORIZATION='invalid_token')
        user, token = self.authentication.authenticate(request)
        self.assertIsNone(user)
        self.assertEqual(token, 'invalid_token')

    def test_authenticate_with_no_token(self):
        request = self.factory.get('/institute/')
        user, token = self.authentication.authenticate(request)
        self.assertIsNone(user)
        self.assertIsNone(token)
