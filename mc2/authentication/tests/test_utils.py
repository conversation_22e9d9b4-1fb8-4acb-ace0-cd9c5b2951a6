from django.test import TestCase
from django.contrib.auth import get_user_model

from ..utils import handle_user_from_next_api


class TestGetOrCreateUserFromNextApi(TestCase):

    def test_returns_none_for_non_mc2_user(self):
        next_api_data = dict(
            type={'amministrazione': 4},
            usr='testuser',
            couch_id='testcouchid'
        )
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_none_for_missing_all_field(self):
        next_api_data = {}
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_none_for_missing_couch_id(self):
        next_api_data = dict(
            type={'mc2': 4},
            usr='testuser',
            couch_id=''
        )
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_create_new_mc2_user(self):
        next_api_data = dict(
            type={'mc2': 4},
            usr='testuser',
            couch_id='couch_id',
            first_name='test',
            surname='user',
            email='<EMAIL>'
        )
        user = handle_user_from_next_api(next_api_data)
        self.assertIsNotNone(user)
        self.assertIsInstance(user, get_user_model())
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.couch_id, 'couch_id')
        self.assertFalse(user.is_superuser)
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertEqual(user.first_name, 'test')
        self.assertEqual(user.last_name, 'user')
        self.assertEqual(user.email, '<EMAIL>')

    def test_returns_create_new_superuser(self):
        next_api_data = dict(
            type={'sadmin': 4},
            usr='testuser',
            couch_id='couch_id'
        )
        user = handle_user_from_next_api(next_api_data)
        self.assertIsNotNone(user)
        self.assertIsInstance(user, get_user_model())
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_active)
        self.assertTrue(user.is_staff)

    def test_returns_updated_user(self):
        get_user_model().objects.create(
            username='testuser',
            couch_id='couch_id'
        )
        next_api_data = dict(
            type={'sadmin': 4},
            usr='testuser_edit',
            couch_id='couch_id',
            first_name='test',
            surname='user',
            email='<EMAIL>'
        )
        user = handle_user_from_next_api(next_api_data)
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'testuser_edit')
        self.assertEqual(user.first_name, 'test')
        self.assertEqual(user.last_name, 'user')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.couch_id, 'couch_id')

    def test_returns_none_for_missing_type_field(self):
        """Test che la funzione ritorni None quando manca il campo 'type'."""
        next_api_data = {
            'usr': 'testuser',
            'couch_id': 'test_couch_id'
        }
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_none_for_missing_usr_field(self):
        """Test che la funzione ritorni None quando manca il campo 'usr'."""
        next_api_data = {
            'type': {'mc2': 4},
            'couch_id': 'test_couch_id'
        }
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_none_for_empty_usr_field(self):
        """Test che la funzione ritorni None quando il campo 'usr' è vuoto."""
        next_api_data = {
            'type': {'mc2': 4},
            'usr': '',
            'couch_id': 'test_couch_id'
        }
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_none_for_none_couch_id(self):
        """Test che la funzione ritorni None quando couch_id è None."""
        next_api_data = {
            'type': {'mc2': 4},
            'usr': 'testuser',
            'couch_id': None
        }
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_creates_user_with_both_mc2_and_sadmin_type(self):
        """Test che la funzione funzioni quando type contiene sia mc2 che sadmin."""
        next_api_data = {
            'type': {'mc2': 4, 'sadmin': 1},
            'usr': 'testuser',
            'couch_id': 'test_couch_id'
        }
        user = handle_user_from_next_api(next_api_data)
        self.assertIsNotNone(user)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_staff)

    def test_creates_user_with_minimal_data(self):
        """Test creazione utente con dati minimi richiesti."""
        next_api_data = {
            'type': {'mc2': 4},
            'usr': 'minimal_user',
            'couch_id': 'minimal_couch_id'
        }
        user = handle_user_from_next_api(next_api_data)
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'minimal_user')
        self.assertEqual(user.couch_id, 'minimal_couch_id')
        self.assertEqual(user.first_name, '')
        self.assertEqual(user.last_name, '')
        self.assertEqual(user.email, '')
        self.assertFalse(user.is_superuser)
        self.assertFalse(user.is_staff)

    def test_updates_existing_user_partial_fields(self):
        """Test aggiornamento parziale di un utente esistente."""
        # Crea utente iniziale
        User = get_user_model()
        existing_user = User.objects.create(
            username='original_user',
            couch_id='test_couch_id',
            first_name='Original',
            last_name='User',
            email='<EMAIL>'
        )

        # Aggiorna solo alcuni campi
        next_api_data = {
            'type': {'mc2': 4},
            'usr': 'updated_user',
            'couch_id': 'test_couch_id',
            'first_name': 'Updated'
            # surname e email non forniti
        }

        user = handle_user_from_next_api(next_api_data)
        self.assertEqual(user.id, existing_user.id)  # Stesso utente
        self.assertEqual(user.username, 'updated_user')
        self.assertEqual(user.first_name, 'Updated')
        self.assertEqual(user.last_name, 'User')  # Non cambiato
        self.assertEqual(user.email, '<EMAIL>')  # Non cambiato

    def test_does_not_update_when_values_are_same(self):
        """Test che non venga fatto update se i valori sono già uguali."""
        User = get_user_model()
        existing_user = User.objects.create(
            username='same_user',
            couch_id='test_couch_id',
            first_name='Same',
            last_name='User',
            email='<EMAIL>'
        )

        # Fornisce gli stessi dati
        next_api_data = {
            'type': {'mc2': 4},
            'usr': 'same_user',
            'couch_id': 'test_couch_id',
            'first_name': 'Same',
            'surname': 'User',
            'email': '<EMAIL>'
        }

        user = handle_user_from_next_api(next_api_data)
        self.assertEqual(user.id, existing_user.id)
        # Verifica che i valori siano rimasti gli stessi
        self.assertEqual(user.username, 'same_user')
        self.assertEqual(user.first_name, 'Same')
        self.assertEqual(user.last_name, 'User')
        self.assertEqual(user.email, '<EMAIL>')

    def test_handles_empty_string_fields_in_update(self):
        """Test gestione campi vuoti durante l'aggiornamento."""
        User = get_user_model()
        User.objects.create(
            username='test_user',
            couch_id='test_couch_id',
            first_name='Original',
            last_name='User'
        )

        # Fornisce campi vuoti - non dovrebbero aggiornare
        next_api_data = {
            'type': {'mc2': 4},
            'usr': 'test_user',
            'couch_id': 'test_couch_id',
            'first_name': '',  # Campo vuoto
            'surname': '',     # Campo vuoto
            'email': ''        # Campo vuoto
        }

        user = handle_user_from_next_api(next_api_data)
        self.assertEqual(user.first_name, '')
        self.assertEqual(user.last_name, '')
        self.assertEqual(user.email, '')

    def test_promotes_regular_user_to_superuser(self):
        """Test promozione di un utente normale a superuser."""
        User = get_user_model()
        existing_user = User.objects.create(
            username='regular_user',
            couch_id='test_couch_id',
            is_superuser=False,
            is_staff=False
        )

        next_api_data = {
            'type': {'sadmin': 1},
            'usr': 'regular_user',
            'couch_id': 'test_couch_id'
        }

        user = handle_user_from_next_api(next_api_data)
        self.assertEqual(user.id, existing_user.id)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_staff)


