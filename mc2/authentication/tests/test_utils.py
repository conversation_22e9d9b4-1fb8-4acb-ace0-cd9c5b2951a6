from django.test import TestCase
from django.contrib.auth import get_user_model

from ..utils import handle_user_from_next_api


class TestGetOrCreateUserFromNextApi(TestCase):

    def test_returns_none_for_non_mc2_user(self):
        next_api_data = dict(
            type={'amministrazione': 4},
            usr='testuser',
            couch_id='testcouchid'
        )
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_none_for_missing_all_field(self):
        next_api_data = {}
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_none_for_missing_couch_id(self):
        next_api_data = dict(
            type={'mc2': 4},
            usr='testuser',
            couch_id=''
        )
        result = handle_user_from_next_api(next_api_data)
        self.assertIsNone(result)

    def test_returns_create_new_mc2_user(self):
        next_api_data = dict(
            type={'mc2': 4},
            usr='testuser',
            couch_id='couch_id',
            first_name='test',
            surname='user',
            email='<EMAIL>'
        )
        user = handle_user_from_next_api(next_api_data)
        self.assertIsNotNone(user)
        self.assertIsInstance(user, get_user_model())
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.couch_id, 'couch_id')
        self.assertFalse(user.is_superuser)
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertEqual(user.first_name, 'test')
        self.assertEqual(user.last_name, 'user')
        self.assertEqual(user.email, '<EMAIL>')

    def test_returns_create_new_superuser(self):
        next_api_data = dict(
            type={'sadmin': 4},
            usr='testuser',
            couch_id='couch_id'
        )
        user = handle_user_from_next_api(next_api_data)
        self.assertIsNotNone(user)
        self.assertIsInstance(user, get_user_model())
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_active)
        self.assertTrue(user.is_staff)

    def test_returns_updated_user(self):
        get_user_model().objects.create(
            username='testuser',
            couch_id='couch_id'
        )
        next_api_data = dict(
            type={'sadmin': 4},
            usr='testuser_edit',
            couch_id='couch_id',
            first_name='test',
            surname='user',
            email='<EMAIL>'
        )
        user = handle_user_from_next_api(next_api_data)
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'testuser_edit')
        self.assertEqual(user.first_name, 'test')
        self.assertEqual(user.last_name, 'user')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.couch_id, 'couch_id')


