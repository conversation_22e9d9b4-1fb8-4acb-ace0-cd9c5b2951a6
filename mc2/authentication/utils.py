from django.contrib.auth import get_user_model

def handle_user_from_next_api(next_api_data: dict):
    User = get_user_model()
    if (
        "type" not in next_api_data
        or not any(k in next_api_data["type"] for k in ("mc2", "sadmin"))
        or not next_api_data.get("usr")
        or not next_api_data.get("couch_id")
    ):
        return None

    user, created = User.objects.get_or_create(
        couch_id=next_api_data["couch_id"],
        defaults={
            "username": next_api_data["usr"],
            "first_name": next_api_data.get("first_name") or "",
            "last_name": next_api_data.get("surname") or "",
            "email": next_api_data.get("email") or "",
        },
    )

    update = False
    if 'usr' in next_api_data.keys() and next_api_data['usr'] != user.username:
        user.username = next_api_data['usr']
        update = True
    if 'first_name' in next_api_data.keys() and next_api_data['first_name'] != user.first_name:
        user.first_name = next_api_data['first_name']
        update = True
    if 'surname' in next_api_data.keys() and next_api_data['surname'] != user.last_name:
        user.last_name = next_api_data['surname']
        update = True
    if 'email' in next_api_data.keys() and next_api_data['email'] != user.email:
        user.email = next_api_data['email']
        update = True
    if 'sadmin' in next_api_data['type'].keys():
        user.is_superuser = True
        user.is_staff = True
        update = True

    if update or created:
        user.save()

    return user
